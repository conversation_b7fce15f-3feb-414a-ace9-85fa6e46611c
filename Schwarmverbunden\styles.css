/* Font Definitions */
@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-300.woff2') format('woff2');
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('assets/fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Lora';
    src: url('assets/fonts/Lora-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Caveat';
    src: url('assets/fonts/caveat-v18-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    /* Optimize scrolling performance */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Lora', serif;
    line-height: 1.6;
    color: #333;
    background-color: #222222;
    /* Optimize scrolling performance */
    -webkit-overflow-scrolling: touch;
    overflow-x: hidden;
}

/* Unified Heading System - Consistent styling for all headings */
h1, h2, h3, h4 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    color: #333;
    margin: 20px 0 15px 0;
    line-height: 1.3;
}

h1 {
    font-size: 32px;
    color: #222;
    margin: 30px 0 20px 0;
}

h2 {
    font-size: 28px;
    color: #fbc99a;
    margin: 25px 0 18px 0;
}

h3 {
    font-size: 18px;
    color: #333;
    margin: 20px 0 10px 0;
}

h4 {
    font-size: 16px;
    color: #333;
    margin: 15px 0 8px 0;
}



/* Banner */
.banner {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: scroll;
    height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
}

.banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
}

.banner h2 {
    color: #fbc99a;
    font-size: 2.5rem;
    z-index: 1;
    position: relative;
    text-shadow:
        0 0 10px rgba(34, 34, 34, 0.9),
        0 0 20px rgba(34, 34, 34, 0.7),
        2px 2px 4px rgba(34, 34, 34, 0.8);
    margin: 0;
}

.banner .einheitsbutton {
    z-index: 1;
    position: relative;
}

/* Banner content wrapper for perfect centering */
.banner-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    z-index: 1;
    position: relative;
}

.banner-zwischenmenschlichkeit {
    background-image: url('assets/bilder/zwischenmenschlichkeit.png');
}

.banner-zusammenhalt {
    background-image: url('assets/bilder/zusammenhalt.png');
}

.banner-fuehrung {
    background-image: url('assets/bilder/fuehrung.png');
}

.banner-blumenfeld {
    background-image: url('assets/bilder/blumenfeld.png');
}

.banner-weekend {
    background-image: url('assets/bilder/weekend.png');
}

.banner-zukunftinsjetzt {
    background-image: url('assets/bilder/zukunftinsjetzt.png');
}


/* Unified Section Box - Beautiful consistent styling for all sections */
.section-box {
    max-width: 800px;
    margin: 40px auto;
    padding: 30px;
    background: #222222;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(251, 201, 154, 0.3);
    line-height: 1.8;
    position: relative;
    text-align: center; /* Zentriere alle Texte */
}

.section-box p {
    margin-bottom: 15px;
    font-size: 16px;
    color: #fbc99a;
}

/* Reduziere Abstand zwischen Überschriften und nachfolgenden Listen */
.section-box p:has(+ ul),
.section-box p:has(+ ol) {
    margin-bottom: 8px;
}

.section-box strong {
    font-weight: 700;
    color: #fbc99a;
}

.section-box h3 {
    text-align: center; /* Überschriften auch zentriert */
    color: #fbc99a;
}

.section-box ul {
    margin: 20px 0;
    padding-left: 30px;
    text-align: left; /* Aufzählungen bleiben linksbündig */
    display: inline-block; /* Damit die linksbündige Ausrichtung in der zentrierten Box funktioniert */
}

.section-box ol {
    margin: 20px 0;
    padding-left: 30px;
    text-align: left; /* Nummerierte Listen bleiben linksbündig */
    display: inline-block; /* Damit die linksbündige Ausrichtung in der zentrierten Box funktioniert */
}

.section-box li {
    margin-bottom: 10px;
    font-size: 16px;
    color: #fbc99a;
}

/* Modern list styling for Co-Creation Week */
.possibilities-list {
    list-style: none;
    padding-left: 0;
    text-align: left; /* Linksbündig für bessere Lesbarkeit */
    display: inline-block; /* Damit die linksbündige Ausrichtung in der zentrierten Box funktioniert */
}

.possibilities-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    line-height: 1.6;
}

.possibilities-list li::before {
    content: "✦";
    position: absolute;
    left: 0;
    top: 0;
    color: #fbc99a;
    font-size: 18px;
    font-weight: bold;
}

.weekly-schedule {
    list-style: none;
    counter-reset: day-counter;
    padding-left: 0;
    text-align: left; /* Linksbündig für bessere Lesbarkeit */
    display: inline-block; /* Damit die linksbündige Ausrichtung in der zentrierten Box funktioniert */
}

.weekly-schedule li {
    position: relative;
    padding-left: 50px;
    margin-bottom: 20px;
    line-height: 1.6;
    counter-increment: day-counter;
}

.weekly-schedule li::before {
    content: counter(day-counter);
    position: absolute;
    left: 0;
    top: 0;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #fbc99a, #f5b885);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

.facts-list {
    list-style: none;
    padding-left: 0;
    text-align: left; /* Linksbündig für bessere Lesbarkeit */
    display: inline-block; /* Damit die linksbündige Ausrichtung in der zentrierten Box funktioniert */
}

.facts-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 12px;
    line-height: 1.6;
}

.facts-list li::before {
    content: "▸";
    position: absolute;
    left: 0;
    top: 0;
    color: #fbc99a;
    font-size: 16px;
    font-weight: bold;
}

/* Community Section - now uses general centered styling from .section-box */

/* Profile elements in section-box - centered styling */
.section-box .profile-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    border: 2px solid #fbc99a;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.3s ease;
    margin: 0 auto 20px auto;
    display: block;
}

.section-box .profile-image:hover {
    transform: scale(1.05);
}



/* Center social links in section-box */
.section-box .social-links {
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Center einheitsbutton for social links */
.social-links .einheitsbutton {
    margin: 0 auto !important;
    display: inline-block !important;
}

/* Einheitsbutton - Unified button styling for all buttons */
.einheitsbutton {
    display: block;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    margin: auto;
    width: fit-content;
    text-align: center;
    background-color: #fbc99a;
    color: #222222;
    border: 2px solid #fbc99a;
}

/* Standard hover animation - color inversion + scale */
.einheitsbutton:hover {
    background-color: #222222;
    color: #fbc99a;
    border-color: #fbc99a;
    transform: scale(1.05);
}

/* Inactive extension - overrides default active state */
.einheitsbutton.inactive {
    background-color: transparent;
    color: #fbc99a;
    border: 2px dashed #fbc99a;
    opacity: 0.6;
    pointer-events: none;
    cursor: not-allowed;
}

/* Transparent extension - for banner buttons */
.einheitsbutton.transparent {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.einheitsbutton.transparent:hover {
    background-color: white;
    color: #333;
    border-color: white;
    transform: scale(1.05);
}

/* Events Text - simple text without box styling */
.events-text {
    max-width: 800px;
    margin: 40px auto 20px auto;
    padding: 0 30px;
    text-align: center;
    line-height: 1.8;
}

.events-text p {
    margin-bottom: 15px;
    font-size: 16px;
}

/* Events Grid Container - separate container for better width utilization */
.events-grid-container {
    max-width: 1200px;
    margin: 20px auto 40px auto;
    padding: 0 30px;
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    justify-content: center;
    max-width: 100%;
}

/* Limit to maximum 3 columns */
@media (min-width: 1020px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(300px, 380px));
    }
}

/* For very wide screens, allow even more width */
@media (min-width: 1400px) {
    .events-grid {
        grid-template-columns: repeat(3, minmax(300px, 420px));
    }
}

.event-card {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    /* Optimize for scrolling performance */
    will-change: auto;
}

.event-card:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.event-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.event-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}


.event-description {
    font-size: 14px;
    margin-bottom: 15px;
    color: #666;
    flex-grow: 1;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 3px;
    margin-top: 25px;
    flex-wrap: nowrap;
    overflow: hidden;
}

/* Instagram extension - special gradient styling */
.einheitsbutton.instagram {
    border: 2px solid transparent;
    background: linear-gradient(#222222, #222222) padding-box,
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: #e6683c;
}

.einheitsbutton.instagram:hover {
    border: 2px solid transparent;
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) padding-box,
                linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%) border-box;
    color: white;
    transform: scale(1.05);
}

/* YouTube extension - red styling */
.einheitsbutton.youtube {
    background-color: #222222;
    border-color: #FF0000;
    color: #FF0000;
}

.einheitsbutton.youtube:hover {
    background-color: #FF0000;
    border-color: #FF0000;
    color: #222222;
    transform: scale(1.05);
}

/* Spotify extension - green styling */
.einheitsbutton.spotify {
    background-color: #222222;
    border-color: #1DB954;
    color: #1DB954;
}

.einheitsbutton.spotify:hover {
    background-color: #1DB954;
    border-color: #1DB954;
    color: #222222;
    transform: scale(1.05);
}

/* Telegram extension - blue styling */
.einheitsbutton.telegram {
    background-color: #222222;
    border-color: #0088cc;
    color: #0088cc;
}

.einheitsbutton.telegram:hover {
    background-color: #0088cc;
    border-color: #0088cc;
    color: #222222;
    transform: scale(1.05);
}

/* Weekend Page Specific Styles */
.highlights-box {
    background-color: #f8f9fa;
    border-left: 4px solid #fbc99a;
    padding: 25px;
    margin: 30px 0;
    border-radius: 5px;
}

.signature {
    text-align: right;
    margin-top: 40px;
    font-style: italic;
    color: #fbc99a;
    font-size: 18px;
}

.signature a {
    color: #fbc99a;
    text-decoration: none;
    font-style: italic;
}

.signature a:hover {
    color: #fbc99a;
    text-decoration: none;
}

/* Impressum Page Specific Styles - now uses section-box styling */
.section-box h2 {
    margin-bottom: 30px;
    border-bottom: 2px solid #fbc99a;
    padding-bottom: 10px;
}







/* Scrolling Performance Optimizations */
* {
    -webkit-tap-highlight-color: transparent;
}

/* Smooth scrollbar styling for better UX */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(251, 201, 154, 0.6);
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(251, 201, 154, 0.8);
}

/* Mobile-specific optimizations - disable hover effects for better performance */
@media (hover: none) and (pointer: coarse) {
    .einheitsbutton:hover,
    .event-card:hover,
    .profile-image:hover {
        transform: none !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
        background-color: inherit !important;
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    /* Unified heading system - mobile adjustments */
    h1 {
        font-size: 26px;
    }

    h2 {
        font-size: 24px;
    }

    h3 {
        font-size: 16px;
    }

    h4 {
        font-size: 14px;
    }

    .banner h2 {
        font-size: 1.8rem;
    }

    .section-box {
        margin: 20px;
        padding: 20px;
    }

    .events-text {
        margin: 20px;
        padding: 0 20px;
    }

    .events-grid {
        grid-template-columns: 1fr;
    }

    .banner {
        height: 300px;
    }

    /* Mobile-specific banner positioning for better image cropping */
    .banner.banner-zusammenhalt {
        background-size: 200%;
        background-position: center 55%;
    }

    .banner-zwischenmenschlichkeit {
        background-size: 180%;
        background-position: center 65%;
    }

    .banner-zukunftinsjetzt {
        background-position: center 75% !important;
    }

    .section-box .profile-image {
        width: 150px;
        height: 150px;
    }

    .social-links {
        flex-direction: row;
        align-items: flex-start;
        gap: 3px;
    }

    .social-links .einheitsbutton {
        font-size: 12px !important;
        padding: 8px 15px !important;
        flex-shrink: 1;
        min-width: 0;
    }

    .highlights-box,
    .details-box,
    .participation-box {
        margin: 20px 0;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    /* Unified heading system - small mobile adjustments */
    h1 {
        font-size: 22px;
    }

    h2 {
        font-size: 20px;
    }

    h3 {
        font-size: 14px;
    }

    h4 {
        font-size: 13px;
    }

    .banner h2 {
        font-size: 1.5rem;
    }

    .section-box {
        margin: 10px;
        padding: 15px;
    }

    .events-text {
        margin: 10px;
        padding: 0 15px;
    }

    .banner {
        height: 250px;
    }

    /* Enhanced mobile banner positioning for very small screens */
    .banner.banner-zusammenhalt {
        background-size: 220%;
        background-position: center 50%;
    }

    .banner-zwischenmenschlichkeit {
        background-size: 200%;
        background-position: center 60%;
    }

    .banner-zukunftinsjetzt {
        background-position: center 70% !important;
    }

    .social-links .einheitsbutton {
        font-size: 11px !important;
        padding: 6px 12px !important;
    }
}